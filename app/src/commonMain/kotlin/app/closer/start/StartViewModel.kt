package app.closer.start

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.closer.config.domain.repository.LocalConfigRepository
import app.closer.start.StartContract.Effect
import app.closer.start.StartContract.Event
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class StartViewModel(
    private val localConfigRepository: LocalConfigRepository
) : ViewModel() {

    var uiState by mutableStateOf(StartContract.UiState())
        private set

    private val _effect: Channel<Effect> = Channel()
    val effect = _effect.receiveAsFlow()

    init {
        localConfigRepository.observeSelectedOrganization()
            .onEach { savedOrg ->
                println("StartViewModel: Received saved organization: $savedOrg")
                uiState = uiState.copy(
                    savedOrganization = savedOrg?.let {
                        StartContract.SavedOrganization(
                            organizationInfo = StartContract.OrganizationInfo(
                                id = it.organizationInfo.id,
                                name = it.organizationInfo.name,
                                isDefault = it.organizationInfo.isDefault
                            ),
                            environment = it.environment,
                            environmentUrl = it.environmentUrl
                        )
                    }
                )
                println("StartViewModel: Updated UI state, savedOrganization is ${if (uiState.savedOrganization != null) "NOT NULL" else "NULL"}")
            }
            .launchIn(viewModelScope)
    }

    fun onEvent(event: Event) {
        viewModelScope.launch {
            when (event) {
                is Event.OnLeadClicked -> {
                    println("StartViewModel: Lead clicked, savedOrganization is ${if (uiState.savedOrganization != null) "NOT NULL" else "NULL"}")
                    if (uiState.savedOrganization != null) {
                        _effect.send(Effect.NavigateToLead)
                    } else {
                        _effect.send(Effect.ShowConfigurationRequired)
                    }
                }
                is Event.OnAdviserClicked -> {
                    println("StartViewModel: Adviser clicked, savedOrganization is ${if (uiState.savedOrganization != null) "NOT NULL" else "NULL"}")
                    if (uiState.savedOrganization != null) {
                        _effect.send(Effect.NavigateToAdviser)
                    } else {
                        _effect.send(Effect.ShowConfigurationRequired)
                    }
                }
                is Event.OnConfigClicked -> _effect.send(Effect.NavigateToConfig)
            }
        }
    }
}
