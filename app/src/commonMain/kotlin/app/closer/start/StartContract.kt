package app.closer.start

object StartContract {

    data class UiState(
        val savedOrganization: SavedOrganization? = null
    )

    data class SavedOrganization(
        val organizationInfo: OrganizationInfo,
        val environment: String,
        val environmentUrl: String
    )

    data class OrganizationInfo(
        val id: String,
        val name: String,
        val isDefault: Boolean
    )

    sealed interface Event {
        data object OnLeadClicked : Event
        data object OnAdviserClicked : Event
        data object OnConfigClicked : Event
    }

    sealed interface Effect {
        data object NavigateToLead : Effect
        data object NavigateToAdviser : Effect
        data object NavigateToConfig : Effect
    }
}
