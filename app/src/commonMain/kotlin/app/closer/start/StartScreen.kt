package app.closer.start


import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.navigation.NavHostController
import app.closer.navigation.Routes
import kotlinx.coroutines.flow.collectLatest
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun StartScreen(
    navController: NavHostController,
    viewModel: StartViewModel = koinViewModel()
) {
    val snackbarHostState = remember { SnackbarHostState() }

    LaunchedEffect(viewModel.effect) {
        viewModel.effect.collectLatest { effect ->
            when (effect) {
                is StartContract.Effect.NavigateToLead -> {
                    navController.navigate(Routes.LEAD)
                }
                is StartContract.Effect.NavigateToAdviser -> {
                    navController.navigate(Routes.ADVISER)
                }
                is StartContract.Effect.NavigateToConfig -> {
                    navController.navigate(Routes.CONFIG)
                }
                is StartContract.Effect.ShowConfigurationRequired -> {
                    snackbarHostState.showSnackbar(
                        message = "Please configure an organization first",
                        withDismissAction = true
                    )
                }
            }
        }
    }



    StartScreenContent(
        uiState = viewModel.uiState,
        onEvent = viewModel::onEvent,
    )
}
