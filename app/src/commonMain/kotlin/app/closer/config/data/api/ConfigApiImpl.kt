package app.closer.config.data.api

import app.closer.config.data.model.ConfigResponse
import io.ktor.client.HttpClient
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpHeaders
import kotlinx.serialization.json.Json

class ConfigApiImpl(
    private val httpClient: HttpClient,
    private val baseUrl: String = "https://sdk.closerbot.lab.lekta.ai"
) : ConfigApi {

    private val json = Json {
        ignoreUnknownKeys = true
    }

    override suspend fun getConfig(): Result<ConfigResponse> =
        runCatching {
            val apiKey = "9be4f134-992c-4429-9d26-8af37649f402"
            val response = httpClient.get("$baseUrl/config") {
                header("X-API-Key", apiKey)
                header(HttpHeaders.Accept, "application/json")
            }
            val responseBody = response.bodyAsText()

            try {
                json.decodeFromString<ConfigResponse>(responseBody)
            } catch (e: Exception) {
                throw IllegalStateException("Server response is not valid JSON: ${e.message}")
            }
        }
}
