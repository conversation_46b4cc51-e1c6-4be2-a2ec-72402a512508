package app.closer.config.data.repository

import app.closer.config.data.api.ConfigApi
import app.closer.config.domain.model.ClusterInfo
import app.closer.config.domain.model.Config
import app.closer.config.domain.model.OrganizationInfo
import app.closer.config.domain.repository.RemoteConfigRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class RemoteConfigRepositoryImpl(
    private val configApi: ConfigApi
) : RemoteConfigRepository {

    override suspend fun getConfig(): Result<Config> = withContext(Dispatchers.IO) {
        runCatching {
            val apiResponse = configApi.getConfig().getOrThrow()
            
            Config(
                apiVersion = apiResponse.apiVersion,
                clusters = apiResponse.clusters.map { cluster ->
                    ClusterInfo(
                        name = cluster.name,
                        url = cluster.url,
                        organizations = cluster.orgs.map { org ->
                            OrganizationInfo(
                                id = org.id,
                                name = org.name,
                                isDefault = org.default
                            )
                        }
                    )
                }
            )
        }
    }
}
